<template>
  <div class="settings-page">
    <h1>系统设置</h1>
    
    <!-- 个人信息设置 -->
    <el-card style="margin-bottom: 20px;">
      <template #header>
        <span>个人信息</span>
      </template>
      <el-form :model="userInfo" label-width="100px" style="max-width: 500px;">
        <el-form-item label="用户名">
          <el-input v-model="userInfo.username" disabled />
        </el-form-item>
        <el-form-item label="姓名">
          <el-input v-model="userInfo.name" />
        </el-form-item>
        <el-form-item label="角色">
          <el-tag :type="userInfo.role === 'ADMIN' ? 'danger' : 'success'">
            {{ userInfo.role === 'ADMIN' ? '管理员' : '服务员' }}
          </el-tag>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="updateUserInfo" :loading="updating">
            更新信息
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 密码修改 -->
    <el-card style="margin-bottom: 20px;">
      <template #header>
        <span>修改密码</span>
      </template>
      <el-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" label-width="100px" style="max-width: 500px;">
        <el-form-item label="当前密码" prop="currentPassword">
          <el-input v-model="passwordForm.currentPassword" type="password" />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="changePassword" :loading="changingPassword">
            修改密码
          </el-button>
          <el-button @click="resetPasswordForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 系统信息 -->
    <el-card style="margin-bottom: 20px;">
      <template #header>
        <span>系统信息</span>
      </template>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="系统名称">酒店点单系统</el-descriptions-item>
        <el-descriptions-item label="版本号">v1.0.0</el-descriptions-item>
        <el-descriptions-item label="后端框架">Spring Boot</el-descriptions-item>
        <el-descriptions-item label="前端框架">Vue 3 + Element Plus</el-descriptions-item>
        <el-descriptions-item label="数据库">MySQL</el-descriptions-item>
        <el-descriptions-item label="部署时间">{{ new Date().toLocaleString('zh-CN') }}</el-descriptions-item>
      </el-descriptions>
    </el-card>
    
    <!-- 系统操作 -->
    <el-card v-if="userStore.isAdmin">
      <template #header>
        <span>系统操作</span>
      </template>
      <el-space>
        <el-button type="warning" @click="clearCache">清除缓存</el-button>
        <el-button type="info" @click="checkHealth">系统检查</el-button>
        <el-button type="danger" @click="showBackupDialog">数据备份</el-button>
      </el-space>
    </el-card>
    
    <!-- 数据备份对话框 -->
    <el-dialog title="数据备份" v-model="showBackup" width="400px">
      <p>确定要进行数据备份吗？此操作可能需要一些时间。</p>
      <template #footer>
        <el-button @click="showBackup = false">取消</el-button>
        <el-button type="primary" @click="performBackup" :loading="backing">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const updating = ref(false)
const changingPassword = ref(false)
const backing = ref(false)
const showBackup = ref(false)
const passwordFormRef = ref()

const userInfo = reactive({
  username: '',
  name: '',
  role: ''
})

const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 初始化用户信息
const initUserInfo = () => {
  if (userStore.user) {
    Object.assign(userInfo, {
      username: userStore.user.username,
      name: userStore.user.name,
      role: userStore.user.role
    })
  }
}

// 更新用户信息
const updateUserInfo = async () => {
  updating.value = true
  try {
    // 这里应该调用API更新用户信息
    // await updateUser(userStore.user.id, userInfo)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新store中的用户信息
    userStore.user.name = userInfo.name
    
    ElMessage.success('个人信息更新成功')
  } catch (error) {
    ElMessage.error('更新失败')
  } finally {
    updating.value = false
  }
}

// 修改密码
const changePassword = async () => {
  try {
    await passwordFormRef.value.validate()
    changingPassword.value = true
    
    // 这里应该调用API修改密码
    // await changeUserPassword(passwordForm)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('密码修改成功')
    resetPasswordForm()
  } catch (error) {
    if (error.message) {
      ElMessage.error('密码修改失败')
    }
  } finally {
    changingPassword.value = false
  }
}

// 重置密码表单
const resetPasswordForm = () => {
  Object.assign(passwordForm, {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  if (passwordFormRef.value) {
    passwordFormRef.value.clearValidate()
  }
}

// 清除缓存
const clearCache = async () => {
  try {
    await ElMessageBox.confirm('确定要清除系统缓存吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 模拟清除缓存
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('缓存清除成功')
  } catch (error) {
    if (error.message) {
      ElMessage.error('缓存清除失败')
    }
  }
}

// 系统检查
const checkHealth = async () => {
  try {
    // 模拟系统检查
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('系统运行正常')
  } catch (error) {
    ElMessage.error('系统检查失败')
  }
}

// 显示备份对话框
const showBackupDialog = () => {
  showBackup.value = true
}

// 执行备份
const performBackup = async () => {
  backing.value = true
  try {
    // 模拟数据备份
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    ElMessage.success('数据备份完成')
    showBackup.value = false
  } catch (error) {
    ElMessage.error('数据备份失败')
  } finally {
    backing.value = false
  }
}

onMounted(() => {
  initUserInfo()
})
</script>

<style scoped>
.settings-page {
  padding: 20px;
}
</style>
