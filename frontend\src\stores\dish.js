import { defineStore } from 'pinia'
import { ref } from 'vue'
import { 
  getDishes, 
  getDish, 
  createDish, 
  updateDish, 
  deleteDish 
} from '@/api/dish'

export const useDishStore = defineStore('dish', () => {
  const dishes = ref([])
  const currentDish = ref(null)
  const loading = ref(false)
  const total = ref(0)

  const fetchDishes = async (params = {}) => {
    loading.value = true
    try {
      const response = await getDishes(params)
      dishes.value = response.data.list
      total.value = response.data.total
      return response
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const fetchDish = async (id) => {
    loading.value = true
    try {
      const response = await getDish(id)
      currentDish.value = response.data
      return response
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const addDish = async (dishData) => {
    try {
      const response = await createDish(dishData)
      dishes.value.unshift(response.data)
      return response
    } catch (error) {
      throw error
    }
  }

  const editDish = async (id, dishData) => {
    try {
      const response = await updateDish(id, dishData)
      const index = dishes.value.findIndex(dish => dish.id === id)
      if (index !== -1) {
        dishes.value[index] = response.data
      }
      return response
    } catch (error) {
      throw error
    }
  }

  const removeDish = async (id) => {
    try {
      await deleteDish(id)
      dishes.value = dishes.value.filter(dish => dish.id !== id)
    } catch (error) {
      throw error
    }
  }

  const getDishsByCategory = (category) => {
    return dishes.value.filter(dish => dish.category === category)
  }

  const getAvailableDishes = () => {
    return dishes.value.filter(dish => dish.status === 1)
  }

  const fetchAvailableDishes = async () => {
    loading.value = true
    try {
      const response = await getDishes({ status: 1 })
      dishes.value = response.data.list || []
      total.value = response.data.total || 0
      return response
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  return {
    dishes,
    currentDish,
    loading,
    total,
    fetchDishes,
    fetchDish,
    addDish,
    editDish,
    removeDish,
    getDishsByCategory,
    getAvailableDishes,
    fetchAvailableDishes
  }
})
