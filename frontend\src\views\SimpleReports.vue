<template>
  <div class="reports-page">
    <h1>报表统计</h1>
    
    <!-- 时间选择 -->
    <el-card style="margin-bottom: 20px;">
      <el-form inline>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadReports">查询</el-button>
          <el-button @click="resetDate">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;" v-loading="loading">
      <el-col :span="6">
        <el-card>
          <div style="text-align: center;">
            <h2 style="color: #409eff; margin: 0;">{{ reportData.totalOrders || 0 }}</h2>
            <p style="margin: 5px 0 0 0;">总订单数</p>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div style="text-align: center;">
            <h2 style="color: #67c23a; margin: 0;">¥{{ reportData.totalSales || 0 }}</h2>
            <p style="margin: 5px 0 0 0;">总销售额</p>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div style="text-align: center;">
            <h2 style="color: #e6a23c; margin: 0;">¥{{ reportData.avgOrderValue || 0 }}</h2>
            <p style="margin: 5px 0 0 0;">平均订单金额</p>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div style="text-align: center;">
            <h2 style="color: #f56c6c; margin: 0;">{{ reportData.completedOrders || 0 }}</h2>
            <p style="margin: 5px 0 0 0;">已完成订单</p>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 热销菜品 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>热销菜品 TOP 10</span>
          </template>
          <div v-loading="hotDishesLoading">
            <div v-if="hotDishes.length === 0" style="text-align: center; color: #909399; padding: 40px;">
              暂无数据
            </div>
            <div v-else>
              <div 
                v-for="(dish, index) in hotDishes" 
                :key="dish.dishId"
                style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #f0f0f0;"
              >
                <div style="display: flex; align-items: center;">
                  <span 
                    :style="{ 
                      color: index < 3 ? '#f56c6c' : '#909399',
                      fontWeight: index < 3 ? 'bold' : 'normal',
                      marginRight: '10px',
                      minWidth: '20px'
                    }"
                  >
                    {{ index + 1 }}
                  </span>
                  <span>{{ dish.dishName }}</span>
                </div>
                <div>
                  <el-tag type="success">{{ dish.totalQuantity }}份</el-tag>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>时段分析</span>
          </template>
          <div v-loading="hourlyLoading">
            <div v-if="hourlyData.length === 0" style="text-align: center; color: #909399; padding: 40px;">
              暂无数据
            </div>
            <div v-else>
              <div 
                v-for="item in hourlyData" 
                :key="item.hour"
                style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #f0f0f0;"
              >
                <div>
                  <span>{{ item.hour }}:00 - {{ item.hour + 1 }}:00</span>
                </div>
                <div>
                  <el-tag type="info">{{ item.orderCount }}单</el-tag>
                  <el-tag type="success" style="margin-left: 10px;">¥{{ item.totalAmount }}</el-tag>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 导出功能 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <span>数据导出</span>
      </template>
      <el-space>
        <el-button type="primary" @click="exportOrders">导出订单数据</el-button>
        <el-button type="success" @click="exportSales">导出销售报表</el-button>
        <el-button type="warning" @click="exportDishes">导出菜品统计</el-button>
      </el-space>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getOrderStats, getHourlyAnalysis } from '@/api/order'
import { getHotDishes } from '@/api/dish'

const loading = ref(false)
const hotDishesLoading = ref(false)
const hourlyLoading = ref(false)
const dateRange = ref([])

const reportData = reactive({
  totalOrders: 0,
  totalSales: 0,
  avgOrderValue: 0,
  completedOrders: 0
})

const hotDishes = ref([])
const hourlyData = ref([])

// 加载报表数据
const loadReports = async () => {
  loading.value = true
  try {
    const response = await getOrderStats()
    if (response.data) {
      Object.assign(reportData, response.data)
    }
  } catch (error) {
    console.error('加载报表数据失败:', error)
    ElMessage.error('加载报表数据失败')
  } finally {
    loading.value = false
  }
}

// 加载热销菜品
const loadHotDishes = async () => {
  hotDishesLoading.value = true
  try {
    const response = await getHotDishes(10)
    hotDishes.value = response.data || []
  } catch (error) {
    console.error('加载热销菜品失败:', error)
    ElMessage.error('加载热销菜品失败')
  } finally {
    hotDishesLoading.value = false
  }
}

// 加载时段分析
const loadHourlyAnalysis = async () => {
  hourlyLoading.value = true
  try {
    const response = await getHourlyAnalysis()
    hourlyData.value = response.data || []
  } catch (error) {
    console.error('加载时段分析失败:', error)
    ElMessage.error('加载时段分析失败')
  } finally {
    hourlyLoading.value = false
  }
}

// 处理日期变化
const handleDateChange = (dates) => {
  if (dates && dates.length === 2) {
    // 可以根据日期范围重新加载数据
    loadReports()
  }
}

// 重置日期
const resetDate = () => {
  dateRange.value = []
  loadReports()
}

// 导出功能（模拟）
const exportOrders = () => {
  ElMessage.info('订单数据导出功能开发中...')
}

const exportSales = () => {
  ElMessage.info('销售报表导出功能开发中...')
}

const exportDishes = () => {
  ElMessage.info('菜品统计导出功能开发中...')
}

onMounted(() => {
  loadReports()
  loadHotDishes()
  loadHourlyAnalysis()
})
</script>

<style scoped>
.reports-page {
  padding: 20px;
}
</style>
