<template>
  <div class="order-detail-page">
    <div class="page-header">
      <h1>订单详情</h1>
      <el-button @click="$router.go(-1)">返回</el-button>
    </div>
    
    <div v-loading="loading">
      <el-row :gutter="20" v-if="order">
        <!-- 订单基本信息 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>订单信息</span>
            </template>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="订单号">#{{ order.id }}</el-descriptions-item>
              <el-descriptions-item label="桌号">{{ order.tableNumber }}</el-descriptions-item>
              <el-descriptions-item label="服务员">{{ order.userName || '未知' }}</el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="getStatusType(order.status)">
                  {{ getStatusText(order.status) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="总金额">¥{{ order.totalPrice }}</el-descriptions-item>
              <el-descriptions-item label="创建时间">{{ formatDate(order.createdAt) }}</el-descriptions-item>
              <el-descriptions-item label="更新时间">{{ formatDate(order.updatedAt) }}</el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
        
        <!-- 状态操作 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>订单操作</span>
            </template>
            <div class="status-actions">
              <el-button 
                v-if="order.status === 'PENDING'" 
                type="primary" 
                @click="updateStatus('PREPARING')"
                :loading="updating"
              >
                开始准备
              </el-button>
              <el-button 
                v-if="order.status === 'PREPARING'" 
                type="success" 
                @click="updateStatus('COMPLETED')"
                :loading="updating"
              >
                完成订单
              </el-button>
              <el-button 
                v-if="['PENDING', 'PREPARING'].includes(order.status)" 
                type="danger" 
                @click="updateStatus('CANCELLED')"
                :loading="updating"
              >
                取消订单
              </el-button>
              <div v-if="order.status === 'COMPLETED'" style="color: #67c23a;">
                <el-icon><Check /></el-icon>
                订单已完成
              </div>
              <div v-if="order.status === 'CANCELLED'" style="color: #f56c6c;">
                <el-icon><Close /></el-icon>
                订单已取消
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 订单明细 -->
      <el-card style="margin-top: 20px;" v-if="order && order.items">
        <template #header>
          <span>订单明细</span>
        </template>
        <el-table :data="order.items" style="width: 100%">
          <el-table-column prop="dishName" label="菜品名称" />
          <el-table-column prop="price" label="单价" width="100">
            <template #default="{ row }">
              ¥{{ row.price }}
            </template>
          </el-table-column>
          <el-table-column prop="quantity" label="数量" width="80" />
          <el-table-column label="小计" width="100">
            <template #default="{ row }">
              ¥{{ (row.price * row.quantity).toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="notes" label="备注" />
        </el-table>
        
        <!-- 总计 -->
        <div style="text-align: right; margin-top: 20px; font-size: 18px; font-weight: bold;">
          总计：¥{{ order.totalPrice }}
        </div>
      </el-card>
      
      <!-- 空状态 -->
      <el-card v-if="!loading && !order">
        <div style="text-align: center; padding: 40px; color: #909399;">
          <el-icon size="48"><Document /></el-icon>
          <p>订单不存在或已被删除</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check, Close, Document } from '@element-plus/icons-vue'
import { getOrder, updateOrderStatus } from '@/api/order'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const updating = ref(false)
const order = ref(null)

// 获取订单详情
const fetchOrder = async () => {
  loading.value = true
  try {
    const response = await getOrder(route.params.id)
    order.value = response.data
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败')
  } finally {
    loading.value = false
  }
}

// 更新订单状态
const updateStatus = async (status) => {
  try {
    const statusText = getStatusText(status)
    await ElMessageBox.confirm(`确定要将订单状态更改为"${statusText}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    updating.value = true
    await updateOrderStatus(order.value.id, status)
    order.value.status = status
    ElMessage.success('状态更新成功')
  } catch (error) {
    if (error.message) {
      ElMessage.error('状态更新失败')
    }
  } finally {
    updating.value = false
  }
}

// 状态相关
const getStatusType = (status) => {
  const statusMap = {
    'PENDING': 'warning',
    'PREPARING': 'info',
    'COMPLETED': 'success',
    'CANCELLED': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'PENDING': '待处理',
    'PREPARING': '准备中',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消'
  }
  return statusMap[status] || status
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchOrder()
})
</script>

<style scoped>
.order-detail-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
}

.status-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-start;
}

.status-actions .el-button {
  width: 120px;
}
</style>
