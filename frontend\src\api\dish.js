import request from './request'

// 获取菜品列表 - 对应后端 /api/dishes
export const getDishes = (params) => {
  return request({
    url: '/api/dishes',
    method: 'get',
    params
  })
}

// 获取单个菜品
export const getDish = (id) => {
  return request({
    url: `/api/dishes/${id}`,
    method: 'get'
  })
}

// 创建菜品
export const createDish = (data) => {
  return request({
    url: '/api/dishes',
    method: 'post',
    data
  })
}

// 更新菜品
export const updateDish = (id, data) => {
  return request({
    url: `/api/dishes/${id}`,
    method: 'put',
    data
  })
}

// 删除菜品
export const deleteDish = (id) => {
  return request({
    url: `/api/dishes/${id}`,
    method: 'delete'
  })
}

// 获取可用菜品列表
export const getAvailableDishes = () => {
  return request({
    url: '/api/dishes/available',
    method: 'get'
  })
}

// 根据分类获取菜品
export const getDishesByCategory = (category) => {
  return request({
    url: '/api/dishes/category',
    method: 'get',
    params: { category }
  })
}

// 获取菜品分类统计
export const getCategoryStats = () => {
  return request({
    url: '/api/dishes/category-stats',
    method: 'get'
  })
}

// 获取热销菜品
export const getHotDishes = (limit = 10) => {
  return request({
    url: '/api/dishes/hot',
    method: 'get',
    params: { limit }
  })
}

// 上传菜品图片
export const uploadDishImage = (file) => {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/api/dishes/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}


// 上传菜品图片
export const uploadDishImage = (file) => {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url: '/dishes/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取菜品分类统计
export const getDishCategoryStats = () => {
  return request({
    url: '/dishes/category-stats',
    method: 'get'
  })
}
